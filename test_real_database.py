#!/usr/bin/env python3
"""
Test script to verify the real database is working
This will create sample patients and test database operations
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from database.connection import SurrealDBManager
from config import settings

async def test_database_operations():
    """Test real database operations"""
    print("🧪 Testing Real Database Operations")
    print("=" * 50)
    
    # Initialize database manager
    db_manager = SurrealDBManager()
    await db_manager.connect()
    
    print(f"✅ Database connected: {db_manager.connected}")
    print(f"📊 Available tables: {list(db_manager.db.keys())}")
    
    # Test 1: Create a patient
    print("\n🏥 Test 1: Creating a patient...")
    patient_data = {
        "pid": "TEST001",
        "demographics": {
            "age": 35,
            "gender": "female",
            "location": "New York"
        },
        "symptoms": {
            "anxiety": 7,
            "depression": 5,
            "sleep_issues": 8
        },
        "clinical_history": {
            "family_psychiatric_history": True,
            "previous_diagnoses": ["anxiety disorder"],
            "substance_use": False
        },
        "clinician_id": "DR001",
        "is_locked": False,
        "completeness_score": 0.85
    }
    
    patient_id = await db_manager.create_patient(patient_data)
    print(f"✅ Created patient: {patient_id}")
    
    # Test 2: Retrieve the patient
    print("\n🔍 Test 2: Retrieving patient by PID...")
    retrieved_patient = await db_manager.get_patient("TEST001")
    if retrieved_patient:
        print(f"✅ Retrieved patient: {retrieved_patient['pid']}")
        print(f"   Age: {retrieved_patient['demographics']['age']}")
        print(f"   Anxiety level: {retrieved_patient['symptoms']['anxiety']}")
    else:
        print("❌ Failed to retrieve patient")
    
    # Test 3: Update the patient
    print("\n📝 Test 3: Updating patient...")
    update_data = {
        "symptoms": {
            "anxiety": 6,  # Improved
            "depression": 4,  # Improved
            "sleep_issues": 7
        },
        "completeness_score": 0.90
    }
    
    update_success = await db_manager.update_patient("TEST001", update_data)
    print(f"✅ Update successful: {update_success}")
    
    # Verify update
    updated_patient = await db_manager.get_patient("TEST001")
    if updated_patient:
        print(f"   New anxiety level: {updated_patient['symptoms']['anxiety']}")
        print(f"   New completeness score: {updated_patient['completeness_score']}")
    
    # Test 4: Create another patient
    print("\n🏥 Test 4: Creating second patient...")
    patient_data_2 = {
        "pid": "TEST002",
        "demographics": {
            "age": 28,
            "gender": "male",
            "location": "California"
        },
        "symptoms": {
            "anxiety": 4,
            "depression": 6,
            "sleep_issues": 5
        },
        "clinician_id": "DR001",
        "is_locked": True,
        "completeness_score": 0.75
    }
    
    patient_id_2 = await db_manager.create_patient(patient_data_2)
    print(f"✅ Created second patient: {patient_id_2}")
    
    # Test 5: Search patients
    print("\n🔍 Test 5: Searching patients...")
    search_results = await db_manager.search_patients({"clinician_id": "DR001"})
    print(f"✅ Found {len(search_results)} patients for clinician DR001")
    for patient in search_results:
        print(f"   - {patient['pid']}: {patient['demographics']['age']} years old")
    
    # Test 6: Store ML prediction
    print("\n🤖 Test 6: Storing ML prediction...")
    prediction_data = {
        "patient": patient_id,
        "prediction_type": "anxiety_risk",
        "prediction_value": "moderate",
        "confidence_score": 0.82,
        "probability_distribution": {
            "low": 0.15,
            "moderate": 0.67,
            "high": 0.18
        },
        "clinician_feedback": None
    }
    
    prediction_id = await db_manager.store_ml_prediction(prediction_data)
    print(f"✅ Stored ML prediction: {prediction_id}")
    
    # Test 7: Get ML training data
    print("\n📊 Test 7: Getting ML training data...")
    training_data = await db_manager.get_ml_training_data()
    print(f"✅ Retrieved {len(training_data)} records for ML training")
    for record in training_data:
        print(f"   - {record['pid']}: completeness {record['completeness_score']}")
    
    # Final summary
    print("\n📈 Database Summary:")
    print(f"   Total patients: {len(db_manager.db['patients'])}")
    print(f"   Total lab results: {len(db_manager.db['lab_results'])}")
    print(f"   Total ML predictions: {len(db_manager.db['ml_predictions'])}")
    
    await db_manager.disconnect()
    print("\n🎉 All tests completed successfully!")
    print("The database is working with REAL data storage and retrieval!")

if __name__ == "__main__":
    asyncio.run(test_database_operations())
