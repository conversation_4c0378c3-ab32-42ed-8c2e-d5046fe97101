import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List
from config import settings
from utils.logging import logger

# In-memory database storage
_database_storage = {
    "patients": {},
    "lab_results": {},
    "ml_predictions": {}
}

class SurrealDBManager:
    """Real in-memory database manager with persistent storage"""

    def __init__(self, db_url: str = None):
        self.db_url = db_url or settings.surrealdb_url
        self.db = _database_storage
        self.connected = False
    
    async def connect(self):
        """Initialize database connection and schema"""
        try:
            # Initialize in-memory database
            self.db = _database_storage

            # Initialize schema (create indexes and constraints)
            await self._initialize_schema()

            self.connected = True
            logger.info("✓ Real in-memory database connected successfully")
            logger.info(f"Database tables: {list(self.db.keys())}")

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def disconnect(self):
        """Close database connection"""
        if self.connected:
            self.connected = False
            logger.info("In-memory database connection closed")
    
    async def _initialize_schema(self):
        """Initialize database schema if not exists"""
        # Initialize tables if they don't exist
        for table_name in ["patients", "lab_results", "ml_predictions"]:
            if table_name not in self.db:
                self.db[table_name] = {}

        logger.info("Database schema initialized")
        logger.info(f"Available tables: {list(self.db.keys())}")
    
    async def create_patient(self, patient_data: Dict[str, Any]) -> str:
        """Create new patient record"""
        try:
            # Generate unique ID
            patient_id = f"patients:{uuid.uuid4()}"

            # Add metadata
            patient_record = {
                "id": patient_id,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                **patient_data
            }

            # Store in database
            self.db["patients"][patient_id] = patient_record

            logger.info(f"Created patient: {patient_id}")
            logger.info(f"Total patients in database: {len(self.db['patients'])}")

            return patient_id
        except Exception as e:
            logger.error(f"Failed to create patient: {e}")
            raise

    async def get_patient(self, pid: str) -> Optional[Dict[str, Any]]:
        """Retrieve patient by PID"""
        try:
            # Search through all patients for matching PID
            for patient_id, patient_data in self.db["patients"].items():
                if patient_data.get("pid") == pid:
                    logger.info(f"Found patient with PID: {pid}")
                    return patient_data

            logger.info(f"Patient not found with PID: {pid}")
            return None
        except Exception as e:
            logger.error(f"Failed to get patient {pid}: {e}")
            return None

    async def update_patient(self, pid: str, update_data: Dict[str, Any]) -> bool:
        """Update patient record"""
        try:
            # Find patient by PID
            for patient_id, patient_data in self.db["patients"].items():
                if patient_data.get("pid") == pid:
                    # Update the record
                    patient_data.update(update_data)
                    patient_data["updated_at"] = datetime.now().isoformat()

                    logger.info(f"Updated patient with PID: {pid}")
                    return True

            logger.warning(f"Patient not found for update with PID: {pid}")
            return False
        except Exception as e:
            logger.error(f"Failed to update patient {pid}: {e}")
            return False

    async def search_patients(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search patients with filters"""
        try:
            results = []

            for patient_id, patient_data in self.db["patients"].items():
                # Apply filters
                match = True

                if "clinician_id" in filters:
                    if patient_data.get("clinician_id") != filters["clinician_id"]:
                        match = False

                if "date_from" in filters and match:
                    patient_date = patient_data.get("created_at", "")
                    if patient_date < filters["date_from"]:
                        match = False

                if "date_to" in filters and match:
                    patient_date = patient_data.get("created_at", "")
                    if patient_date > filters["date_to"]:
                        match = False

                if match:
                    results.append(patient_data)

            # Sort by created_at descending
            results.sort(key=lambda x: x.get("created_at", ""), reverse=True)

            logger.info(f"Found {len(results)} patients matching filters")
            return results
        except Exception as e:
            logger.error(f"Failed to search patients: {e}")
            return []

    async def get_ml_training_data(self) -> List[Dict[str, Any]]:
        """Retrieve complete data for ML training"""
        try:
            training_data = []

            for _, patient_data in self.db["patients"].items():
                # Filter for locked patients with good completeness
                if (patient_data.get("is_locked", False) and
                    patient_data.get("completeness_score", 0.0) > 0.7):

                    # Get recent lab results for this patient
                    patient_id = patient_data.get("id")
                    recent_labs = []

                    for _, lab_data in self.db["lab_results"].items():
                        if lab_data.get("patient") == patient_id:
                            recent_labs.append(lab_data)

                    # Sort by collection date and take last 5
                    recent_labs.sort(key=lambda x: x.get("collection_date", ""), reverse=True)
                    recent_labs = recent_labs[:5]

                    # Prepare training record
                    training_record = {
                        "pid": patient_data.get("pid"),
                        "demographics": patient_data.get("demographics", {}),
                        "symptoms": patient_data.get("symptoms", {}),
                        "clinical_history": patient_data.get("clinical_history", {}),
                        "recent_labs": recent_labs,
                        "completeness_score": patient_data.get("completeness_score", 0.0)
                    }

                    training_data.append(training_record)

            logger.info(f"Retrieved {len(training_data)} records for ML training")
            return training_data
        except Exception as e:
            logger.error(f"Failed to get ML training data: {e}")
            return []

    async def store_ml_prediction(self, prediction_data: Dict[str, Any]) -> str:
        """Store ML prediction result"""
        try:
            # Generate unique ID
            prediction_id = f"ml_predictions:{uuid.uuid4()}"

            # Add metadata
            prediction_record = {
                "id": prediction_id,
                "prediction_timestamp": datetime.now().isoformat(),
                **prediction_data
            }

            # Store in database
            self.db["ml_predictions"][prediction_id] = prediction_record

            logger.info(f"Stored ML prediction: {prediction_id}")
            logger.info(f"Total predictions in database: {len(self.db['ml_predictions'])}")

            return prediction_id
        except Exception as e:
            logger.error(f"Failed to store ML prediction: {e}")
            raise
