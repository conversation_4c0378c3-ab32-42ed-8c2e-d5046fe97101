2025-08-20 22:51:08 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-20 22:51:08 | ERROR    | database.connection:connect:42 - Failed to connect to SurrealDB: Surreal() missing 1 required positional argument: 'url'
2025-08-20 22:51:08 | ERROR    | main:lifespan:40 - Failed to connect to database: Surreal() missing 1 required positional argument: 'url'
2025-08-20 22:57:59 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-20 22:57:59 | ERROR    | database.connection:connect:42 - Failed to connect to SurrealDB: Surreal() missing 1 required positional argument: 'url'
2025-08-20 22:57:59 | ERROR    | backend.main:lifespan:40 - Failed to connect to database: Surreal() missing 1 required positional argument: 'url'
2025-08-20 22:58:30 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-20 22:58:30 | ERROR    | database.connection:connect:42 - Failed to connect to SurrealDB: Surreal() missing 1 required positional argument: 'url'
2025-08-20 22:58:30 | ERROR    | backend.main:lifespan:40 - Failed to connect to database: Surreal() missing 1 required positional argument: 'url'
2025-08-20 23:03:23 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-20 23:03:23 | ERROR    | database.connection:connect:42 - Failed to connect to SurrealDB: Surreal() missing 1 required positional argument: 'url'
2025-08-20 23:03:23 | ERROR    | backend.main:lifespan:40 - Failed to connect to database: Surreal() missing 1 required positional argument: 'url'
2025-08-20 23:03:43 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-20 23:03:43 | ERROR    | database.connection:connect:42 - Failed to connect to SurrealDB: Surreal() missing 1 required positional argument: 'url'
2025-08-20 23:03:43 | ERROR    | backend.main:lifespan:40 - Failed to connect to database: Surreal() missing 1 required positional argument: 'url'
2025-08-20 23:04:12 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-20 23:04:12 | ERROR    | database.connection:connect:42 - Failed to connect to SurrealDB: Surreal() missing 1 required positional argument: 'url'
2025-08-20 23:04:12 | ERROR    | backend.main:lifespan:40 - Failed to connect to database: Surreal() missing 1 required positional argument: 'url'
2025-08-20 23:47:08 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-20 23:47:08 | ERROR    | database.connection:connect:42 - Failed to connect to SurrealDB: Surreal() missing 1 required positional argument: 'url'
2025-08-20 23:47:08 | ERROR    | backend.main:lifespan:40 - Failed to connect to database: Surreal() missing 1 required positional argument: 'url'
2025-08-21 00:44:01 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-21 00:44:01 | INFO     | database.connection:_initialize_schema:52 - Database schema initialized
2025-08-21 00:44:01 | INFO     | database.connection:_initialize_schema:53 - Available tables: ['patients', 'lab_results', 'ml_predictions']
2025-08-21 00:44:01 | INFO     | database.connection:connect:32 - ✓ Real in-memory database connected successfully
2025-08-21 00:44:01 | INFO     | database.connection:connect:33 - Database tables: ['patients', 'lab_results', 'ml_predictions']
2025-08-21 00:44:01 | INFO     | backend.main:lifespan:38 - ✓ Database connection established
2025-08-21 00:44:01 | INFO     | backend.main:lifespan:51 - No pre-trained models found - will train on first use
2025-08-21 00:44:01 | INFO     | backend.main:lifespan:58 - ✓ Application startup completed
